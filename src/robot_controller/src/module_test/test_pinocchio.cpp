#include "robot_controller/def_class.h"
#include <set>
#include <pinocchio/algorithm/jacobian.hpp>
#include <pinocchio/algorithm/kinematics.hpp>
#include <pinocchio/algorithm/frames.hpp>
#include <pinocchio/algorithm/joint-configuration.hpp>

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "test_pinocchio");
    ros::NodeHandle nh;

    pinocchio::Model model;
    std::string path_pkg = ros::package::getPath("robot_controller");
    std::string path_urdf = path_pkg + "/description/urdf/S1_robot.urdf";
    pinocchio::urdf::buildModel(path_urdf, model);

    pinocchio::Data data(model);

    std::cout << "Total joints: " << model.njoints << std::endl;
    std::cout << "Total DOF (nq): " << model.nq << std::endl;
    std::cout << "Total velocity DOF (nv): " << model.nv << std::endl;

    Eigen::VectorXd q(model.nq);  // 机器人关节数
    q.setZero();  // 假设机器人所有关节都在零位置

    pinocchio::FrameIndex fid = model.getFrameId("r_Link5");
    pinocchio::FrameIndex fid2 = model.getFrameId("r_Link4_mimic");
    pinocchio::FrameIndex fid3 = model.getFrameId("r_Link4");
    ROS_INFO_STREAM("fid: " << fid << " fid2: " << fid2 << " fid3: " << fid3);

    pinocchio::JointIndex jid = model.frames[fid].parent;
    pinocchio::JointIndex jid2 = model.frames[fid2].parent;
    pinocchio::JointIndex jid3 = model.frames[fid3].parent;
    pinocchio::JointIndex jid4 = model.getJointId("base_joint3");
    ROS_INFO_STREAM("jid: " << jid << "[" << model.names[jid] << ", " << model.joints[jid].nq() << "]");
    ROS_INFO_STREAM("jid2: " << jid2 << "[" << model.names[jid2] << ", " << model.joints[jid2].nq() << "]");
    ROS_INFO_STREAM("jid3: " << jid3 << "[" << model.names[jid3] << ", " << model.joints[jid3].nq() << "]");
    ROS_INFO_STREAM("jid4: " << jid4 );


    // pinocchio::forwardKinematics(model, data, q);
    // pinocchio::updateFramePlacements(model, data);
    
    // // 5. 获取末端执行器（例如手臂末端）的变换矩阵
    // pinocchio::SE3Tpl<double> X_ee = data.oMf[fid];
    // Eigen::Matrix4d X_matrix;
    // X_matrix.setIdentity();
    // X_matrix.block<3,3>(0,0) = X_ee.rotation();
    // X_matrix.block<3,1>(0,3) = X_ee.translation();

    // std::cout << "ee_link pose:\n" << X_matrix << std::endl;
    // ROS_INFO_STREAM(X_ee);


    return 0;
}
